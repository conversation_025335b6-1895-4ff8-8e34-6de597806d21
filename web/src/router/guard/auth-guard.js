import { getToken, isNullOrWhitespace } from '@/utils'

const WHITE_LIST = ['/', '/login', '/register', '/reset-password', '/404']
export function createAuthGuard(router) {
  router.beforeEach(async (to) => {
    const token = getToken()

    /** 没有token的情况 */
    if (isNullOrWhitespace(token)) {
      if (WHITE_LIST.includes(to.path)) return true
      return { path: '/login', query: { ...to.query, redirect: to.path } }
    }

    /** 有token的情况 */
    if (to.path === '/login' || to.path === '/register' || to.path === '/reset-password') return { path: '/workbench' }
    return true
  })
}
