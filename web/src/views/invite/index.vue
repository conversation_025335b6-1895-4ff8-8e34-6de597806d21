<template>
  <div class="p-20">
    <n-card title="推广中心" class="mb-20">
      <template #header-extra>
        <n-button @click="refreshData" :loading="loading">
          <template #icon>
            <n-icon><RefreshIcon /></n-icon>
          </template>
          刷新
        </n-button>
      </template>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-16 mb-20">
        <n-card>
          <n-statistic label="总邀请数" :value="statistics.total_invites" />
        </n-card>
        <n-card>
          <n-statistic label="本月邀请" :value="statistics.month_invites" />
        </n-card>
        <n-card>
          <n-statistic label="已奖励邀请" :value="statistics.rewarded_invites" />
        </n-card>
        <n-card>
          <n-statistic label="总奖励积分" :value="statistics.total_rewards" />
        </n-card>
      </div>

      <!-- 邀请信息 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-20">
        <!-- 邀请码 -->
        <n-card title="我的邀请码" size="small">
          <div class="text-center">
            <div class="text-24 font-bold text-primary mb-10">{{ inviteData.invite_code }}</div>
            <n-button @click="copyInviteCode" type="primary" ghost>
              <template #icon>
                <n-icon><CopyIcon /></n-icon>
              </template>
              复制邀请码
            </n-button>
          </div>
        </n-card>

        <!-- 邀请链接 -->
        <n-card title="邀请链接" size="small">
          <div class="mb-10">
            <n-input
              v-model:value="inviteData.invite_link"
              readonly
              placeholder="邀请链接"
            />
          </div>
          <div class="text-center">
            <n-button @click="copyInviteLink" type="primary" ghost>
              <template #icon>
                <n-icon><CopyIcon /></n-icon>
              </template>
              复制邀请链接
            </n-button>
          </div>
        </n-card>
      </div>

      <!-- 邀请说明 -->
      <n-alert type="info" class="mt-20">
        <template #header>邀请奖励说明</template>
        <ul class="list-disc list-inside">
          <li>每成功邀请一位用户注册，您将获得 <strong>1000 积分</strong> 奖励</li>
          <li>被邀请用户注册成功后，奖励积分会自动发放到您的账户</li>
          <li>分享您的邀请码或邀请链接给朋友，让他们在注册时填写您的邀请码</li>
          <li>积分可用于使用平台的各项AI服务</li>
        </ul>
      </n-alert>
    </n-card>

    <!-- 邀请记录 -->
    <n-card title="邀请记录">
      <n-data-table
        :columns="columns"
        :data="inviteRecords"
        :loading="tableLoading"
        :pagination="pagination"
        :remote="true"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
  </div>
</template>

<script setup>
import { NIcon } from 'naive-ui'
import { Refresh as RefreshIcon, Copy as CopyIcon } from '@vicons/ionicons5'
import api from '@/api'

const inviteData = ref({
  invite_code: '',
  invite_link: '',
  total_invites: 0,
  total_rewards: 0,
  invite_records: []
})

const statistics = ref({
  total_invites: 0,
  month_invites: 0,
  rewarded_invites: 0,
  total_rewards: 0
})

const inviteRecords = ref([])
const loading = ref(false)
const tableLoading = ref(false)

const pagination = ref({
  page: 1,
  pageSize: 20,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100]
})

// 表格列定义
const columns = [
  {
    title: '被邀请人',
    key: 'invitee_username',
    width: 120
  },
  {
    title: '邮箱',
    key: 'invitee_email',
    width: 200
  },
  {
    title: '邀请码',
    key: 'invite_code',
    width: 100
  },
  {
    title: '奖励积分',
    key: 'reward_credits',
    width: 100,
    render: (row) => {
      return row.reward_credits
    }
  },
  {
    title: '奖励状态',
    key: 'is_rewarded',
    width: 100,
    render: (row) => {
      return h(
        'n-tag',
        {
          type: row.is_rewarded ? 'success' : 'warning'
        },
        row.is_rewarded ? '已发放' : '待发放'
      )
    }
  },
  {
    title: '注册时间',
    key: 'created_at',
    width: 160,
    render: (row) => {
      return new Date(row.created_at).toLocaleString()
    }
  }
]

// 初始化
onMounted(() => {
  loadData()
})

// 加载数据
async function loadData() {
  await Promise.all([
    loadInviteData(),
    loadStatistics(),
    loadInviteRecords()
  ])
}

// 加载邀请数据
async function loadInviteData() {
  try {
    loading.value = true
    const res = await api.getMyInviteData()
    if (res.code === 200) {
      inviteData.value = res.data
    }
  } catch (error) {
    console.error('加载邀请数据失败:', error)
    $message.error('加载邀请数据失败')
  } finally {
    loading.value = false
  }
}

// 加载统计数据
async function loadStatistics() {
  try {
    const res = await api.getMyInviteStatistics()
    if (res.code === 200) {
      statistics.value = res.data
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载邀请记录
async function loadInviteRecords() {
  try {
    tableLoading.value = true
    const res = await api.getMyInviteRecords({
      page: pagination.value.page,
      page_size: pagination.value.pageSize
    })
    if (res.code === 200) {
      inviteRecords.value = res.data
      pagination.value.itemCount = res.total
    }
  } catch (error) {
    console.error('加载邀请记录失败:', error)
    $message.error('加载邀请记录失败')
  } finally {
    tableLoading.value = false
  }
}

// 刷新数据
function refreshData() {
  loadData()
}

// 复制邀请码
async function copyInviteCode() {
  try {
    await navigator.clipboard.writeText(inviteData.value.invite_code)
    $message.success('邀请码已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    $message.error('复制失败，请手动复制')
  }
}

// 复制邀请链接
async function copyInviteLink() {
  try {
    await navigator.clipboard.writeText(inviteData.value.invite_link)
    $message.success('邀请链接已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    $message.error('复制失败，请手动复制')
  }
}

// 分页处理
function handlePageChange(page) {
  pagination.value.page = page
  loadInviteRecords()
}

function handlePageSizeChange(pageSize) {
  pagination.value.pageSize = pageSize
  pagination.value.page = 1
  loadInviteRecords()
}
</script>

<style scoped>
.text-primary {
  color: #18a058;
}
</style>
