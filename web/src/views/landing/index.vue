<template>
  <div class="landing-page">
    <!-- 顶部导航 -->
    <header class="header">
      <div class="header-content">
        <div class="logo-section">
          <icon-custom-logo class="logo-icon" />
          <span class="logo-text">AI-clear</span>
          <span class="tagline">学术道</span>
        </div>
        <div class="nav-buttons">
          <n-button text class="nav-link" @click="scrollToSection('features')">
            产品功能
          </n-button>
          <n-button text class="nav-link" @click="scrollToSection('about')">
            关于我们
          </n-button>
          <n-button class="login-btn" @click="goToLogin">
            登录体验
          </n-button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="hero-content">
          <div class="hero-text">
            <div class="hero-logo">
              <icon-custom-logo class="hero-logo-icon" />
              <h1 class="hero-title">AI</h1>
            </div>
            <p class="hero-subtitle">中国权威专业的降重、降AI率平台</p>
            
            <!-- 功能特色卡片 -->
            <div class="feature-cards">
              <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3>降重检测</h3>
                <p>基于深度学习的智能降重技术，精准识别重复内容</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3>降AI率</h3>
                <p>有效规避AI检测，确保内容原创性和学术价值</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3>快速出结果</h3>
                <p>高效处理，快速生成结果，节省宝贵时间</p>
              </div>
              <div class="feature-card">
                <div class="feature-icon">💰</div>
                <h3>高性价比</h3>
                <p>优质服务，合理定价，为学术研究提供经济支持</p>
              </div>
            </div>

            <div class="cta-buttons">
              <n-button type="primary" size="large" class="cta-primary" @click="goToRegister">
                立即注册
              </n-button>
              <n-button size="large" class="cta-secondary" @click="goToLogin">
                登录体验
              </n-button>
            </div>
          </div>
          
          <div class="hero-image">
            <div class="hero-visual">
              <div class="floating-card card-1">
                <div class="card-icon">📄</div>
                <div class="card-text">智能文档处理</div>
              </div>
              <div class="floating-card card-2">
                <div class="card-icon">🎯</div>
                <div class="card-text">精准降重检测</div>
              </div>
              <div class="floating-card card-3">
                <div class="card-icon">✨</div>
                <div class="card-text">AI内容优化</div>
              </div>
              <div class="main-visual">
                <icon-custom-front-page class="main-visual-icon" />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 产品介绍 -->
      <section id="features" class="features-section">
        <div class="section-content">
          <h2 class="section-title">更学术、更专业的降重降AI率服务</h2>
          <p class="section-subtitle">大模型支持：有道学术降重、DeepSeek</p>
          
          <div class="features-grid">
            <div class="features-text">
              <div class="feature-item">
                <div class="feature-check">✓</div>
                <div class="feature-content">
                  <h4>资深学术资源优势</h4>
                  <p>网易有道深耕学术教育领域10年，深度理解Top20高校师生需求，海外外500+高校师生信赖</p>
                </div>
              </div>
              
              <div class="feature-item">
                <div class="feature-check">✓</div>
                <div class="feature-content">
                  <h4>高质量文本表达</h4>
                  <p>诸如500+学科专业术语库，论文表达更加专业，文本表达更符合专业人工表达习惯</p>
                </div>
              </div>
              
              <div class="feature-item">
                <div class="feature-check">✓</div>
                <div class="feature-content">
                  <h4>先进大模型技术</h4>
                  <p>自研学术语言文本改写解析模型，获取自然语言处理专利，学术文本专业性和可读性兼具</p>
                </div>
              </div>
              
              <div class="feature-item">
                <div class="feature-check">✓</div>
                <div class="feature-content">
                  <h4>高效成果看得见</h4>
                  <p>经权威系统检测，重复率、AI率切实降低，显著提升论文质量，保持高质量学术表达</p>
                </div>
              </div>
            </div>
            
            <div class="features-image">
              <div class="product-showcase">
                <div class="showcase-header">
                  <div class="showcase-logo">
                    <icon-custom-logo />
                    <span>AI-clear</span>
                  </div>
                  <div class="showcase-subtitle">智能降重 · 降AI率</div>
                </div>
                <div class="showcase-content">
                  <div class="processing-animation">
                    <div class="processing-step active">
                      <div class="step-icon">📤</div>
                      <div class="step-text">上传文档</div>
                    </div>
                    <div class="processing-arrow">→</div>
                    <div class="processing-step active">
                      <div class="step-icon">🔄</div>
                      <div class="step-text">智能处理</div>
                    </div>
                    <div class="processing-arrow">→</div>
                    <div class="processing-step">
                      <div class="step-icon">📥</div>
                      <div class="step-text">获取结果</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 底部 -->
    <footer id="about" class="footer">
      <div class="footer-content">
        <div class="footer-main">
          <div class="footer-logo">
            <icon-custom-logo class="footer-logo-icon" />
            <div class="footer-brand">
              <h3>AI-clear</h3>
              <p>中国权威专业的降重、降AI率平台</p>
            </div>
          </div>
          
          <div class="footer-info">
            <div class="company-info">
              <p>北京网易有道计算机系统有限公司</p>
              <p class="company-details">
                京ICP备05000000号 | 京公网安备 11010000000000号 | 网络文化经营许可证 | 
                <EMAIL>
              </p>
            </div>
            
            <div class="contact-links">
              <div class="contact-item">
                <span class="contact-label">关注我们</span>
              </div>
              <div class="social-links">
                <div class="social-link">
                  <div class="social-icon wechat">微信</div>
                </div>
                <div class="social-link">
                  <div class="social-icon qq">QQ</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
const router = useRouter()

// 导航到登录页
function goToLogin() {
  router.push('/login')
}

// 导航到注册页
function goToRegister() {
  router.push('/register')
}

// 滚动到指定区域
function scrollToSection(sectionId) {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  color: white;
  overflow-x: hidden;
}

/* 顶部导航 */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 32px;
  color: #4f9cf9;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: white;
}

.tagline {
  font-size: 14px;
  color: #4f9cf9;
  background: rgba(79, 156, 249, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.nav-link:hover {
  color: #4f9cf9;
}

.login-btn {
  background: #4f9cf9;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
}

.login-btn:hover {
  background: #3d8ce6;
}

/* 主要内容 */
.main-content {
  padding-top: 70px;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: 40px 20px;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  max-width: 600px;
}

.hero-logo {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.hero-logo-icon {
  font-size: 48px;
  color: #4f9cf9;
}

.hero-title {
  font-size: 48px;
  font-weight: bold;
  margin: 0;
  background: linear-gradient(135deg, #4f9cf9, #74b9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin: 20px 0 40px 0;
  line-height: 1.6;
}

/* 功能特色卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin: 40px 0;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: #4f9cf9;
  transform: translateY(-2px);
}

.feature-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.feature-card h3 {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.feature-card p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.4;
}

/* CTA 按钮 */
.cta-buttons {
  display: flex;
  gap: 16px;
  margin-top: 40px;
}

.cta-primary {
  background: #4f9cf9;
  border: none;
  padding: 12px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.cta-primary:hover {
  background: #3d8ce6;
}

.cta-secondary {
  background: transparent;
  border: 2px solid #4f9cf9;
  color: #4f9cf9;
  padding: 10px 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
}

.cta-secondary:hover {
  background: rgba(79, 156, 249, 0.1);
}

/* Hero 图像区域 */
.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-visual {
  position: relative;
  width: 400px;
  height: 400px;
}

.main-visual {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: rgba(79, 156, 249, 0.1);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(79, 156, 249, 0.3);
}

.main-visual-icon {
  font-size: 80px;
  color: #4f9cf9;
}

.floating-card {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  animation: float 3s ease-in-out infinite;
}

.card-1 {
  top: 20px;
  left: 20px;
  animation-delay: 0s;
}

.card-2 {
  top: 20px;
  right: 20px;
  animation-delay: 1s;
}

.card-3 {
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 2s;
}

.card-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.card-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.card-3 {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateX(-50%) translateY(0px);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

/* 产品功能区域 */
.features-section {
  background: rgba(255, 255, 255, 0.02);
  padding: 80px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.section-content {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 36px;
  font-weight: bold;
  text-align: center;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #4f9cf9, #74b9ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  margin: 0 0 60px 0;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.features-text {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.feature-item {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.feature-check {
  width: 24px;
  height: 24px;
  background: #4f9cf9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  color: white;
  flex-shrink: 0;
  margin-top: 2px;
}

.feature-content h4 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.feature-content p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  line-height: 1.6;
}

/* 产品展示 */
.features-image {
  display: flex;
  justify-content: center;
}

.product-showcase {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  width: 100%;
  max-width: 400px;
  backdrop-filter: blur(10px);
}

.showcase-header {
  text-align: center;
  margin-bottom: 32px;
}

.showcase-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 12px;
}

.showcase-logo svg {
  font-size: 32px;
  color: #4f9cf9;
}

.showcase-logo span {
  font-size: 20px;
  font-weight: bold;
  color: white;
}

.showcase-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.processing-animation {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.processing-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.processing-step.active {
  opacity: 1;
}

.step-icon {
  width: 48px;
  height: 48px;
  background: rgba(79, 156, 249, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  border: 2px solid rgba(79, 156, 249, 0.3);
}

.processing-step.active .step-icon {
  background: rgba(79, 156, 249, 0.3);
  border-color: #4f9cf9;
}

.step-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.processing-arrow {
  color: rgba(255, 255, 255, 0.3);
  font-size: 16px;
  margin: 0 8px;
}

/* 底部 */
.footer {
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px 20px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.footer-logo-icon {
  font-size: 32px;
  color: #4f9cf9;
}

.footer-brand h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 0 0 8px 0;
  color: white;
}

.footer-brand p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-end;
}

.company-info {
  text-align: right;
}

.company-info p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 8px 0;
}

.company-details {
  font-size: 12px !important;
  color: rgba(255, 255, 255, 0.5) !important;
}

.contact-links {
  display: flex;
  align-items: center;
  gap: 16px;
}

.contact-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-icon {
  font-size: 12px;
  font-weight: 500;
}

.social-icon.wechat {
  background: #07c160;
  color: white;
}

.social-icon.qq {
  background: #12b7f5;
  color: white;
}

.social-link:hover {
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .nav-buttons {
    gap: 12px;
  }

  .nav-link {
    display: none;
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-title {
    font-size: 36px;
  }

  .feature-cards {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-main {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 24px;
  }

  .footer-info {
    align-items: center;
  }

  .company-info {
    text-align: center;
  }
}
</style>
