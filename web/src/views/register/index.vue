<template>
  <AppPage :show-footer="true" bg-cover :style="{ backgroundImage: `url(${bgImg})` }">
    <div
      style="transform: translateY(25px)"
      class="m-auto max-w-1500 min-w-345 f-c-c rounded-10 bg-white bg-opacity-60 p-15 card-shadow"
      dark:bg-dark
    >
      <div hidden w-380 px-20 py-35 md:block>
        <icon-custom-front-page pt-10 text-300 color-primary></icon-custom-front-page>
      </div>

      <div w-320 flex-col px-20 py-35>
        <h5 f-c-c text-24 font-normal color="#6a6a6a">
          <icon-custom-logo mr-10 text-50 color-primary />{{ $t('app_name') }} - 注册
        </h5>
        
        <div mt-30>
          <n-input
            v-model:value="registerInfo.email"
            class="h-50 items-center pl-10 text-16"
            placeholder="请输入邮箱地址"
            :maxlength="255"
            @blur="checkEmail"
          />
          <div v-if="emailError" class="text-red-500 text-12 mt-5">{{ emailError }}</div>
        </div>

        <div mt-20>
          <n-input
            v-model:value="registerInfo.username"
            class="h-50 items-center pl-10 text-16"
            placeholder="请输入用户名"
            :maxlength="20"
            @blur="checkUsername"
          />
          <div v-if="usernameError" class="text-red-500 text-12 mt-5">{{ usernameError }}</div>
        </div>

        <div mt-20>
          <n-input
            v-model:value="registerInfo.password"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="请输入密码（至少6位）"
            :maxlength="50"
          />
        </div>

        <div mt-20>
          <n-input
            v-model:value="registerInfo.confirmPassword"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="请确认密码"
            :maxlength="50"
          />
        </div>

        <div mt-20>
          <div class="flex items-center gap-10">
            <n-input
              v-model:value="registerInfo.verificationCode"
              class="h-50 items-center pl-10 text-16 flex-1"
              placeholder="请输入邮箱验证码"
              :maxlength="6"
            />
            <n-button
              class="h-50 w-120"
              :disabled="!canSendCode || sendCodeLoading"
              :loading="sendCodeLoading"
              @click="sendVerificationCode"
            >
              {{ sendCodeText }}
            </n-button>
          </div>
        </div>

        <div mt-20>
          <n-input
            v-model:value="registerInfo.inviteCode"
            class="h-50 items-center pl-10 text-16"
            placeholder="邀请码（可选）"
            :maxlength="20"
            @blur="validateInviteCode"
          />
          <div v-if="inviteCodeInfo" class="text-green-600 text-12 mt-5">
            邀请人: {{ inviteCodeInfo.inviter_username }}
          </div>
          <div v-if="inviteCodeError" class="text-red-500 text-12 mt-5">{{ inviteCodeError }}</div>
        </div>

        <div mt-20>
          <n-button
            h-50
            w-full
            rounded-5
            text-16
            type="primary"
            :loading="registerLoading"
            @click="handleRegister"
          >
            注册
          </n-button>
        </div>

        <div mt-20 text-center>
          <n-button text @click="goToLogin">
            已有账户？立即登录
          </n-button>
        </div>
      </div>
    </div>
  </AppPage>
</template>

<script setup>
import { lStorage } from '@/utils'
import bgImg from '@/assets/images/login_bg.webp'
import api from '@/api'

const router = useRouter()
const route = useRoute()

const registerInfo = ref({
  email: '',
  username: '',
  password: '',
  confirmPassword: '',
  verificationCode: '',
  inviteCode: ''
})

const emailError = ref('')
const usernameError = ref('')
const inviteCodeError = ref('')
const inviteCodeInfo = ref(null)

const registerLoading = ref(false)
const sendCodeLoading = ref(false)
const sendCodeCountdown = ref(0)

// 初始化邀请码
onMounted(() => {
  if (route.query.aff) {
    registerInfo.value.inviteCode = route.query.aff
    validateInviteCode()
  }
})

// 发送验证码按钮文本
const sendCodeText = computed(() => {
  if (sendCodeCountdown.value > 0) {
    return `${sendCodeCountdown.value}s后重发`
  }
  return '发送验证码'
})

// 是否可以发送验证码
const canSendCode = computed(() => {
  return registerInfo.value.email && 
         !emailError.value && 
         sendCodeCountdown.value === 0
})

// 检查邮箱
async function checkEmail() {
  if (!registerInfo.value.email) {
    emailError.value = ''
    return
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(registerInfo.value.email)) {
    emailError.value = '请输入有效的邮箱地址'
    return
  }

  try {
    const res = await api.checkEmail({ email: registerInfo.value.email })
    if (res.data.exists) {
      emailError.value = '该邮箱已注册，请直接登录'
    } else {
      emailError.value = ''
    }
  } catch (error) {
    console.error('检查邮箱失败:', error)
  }
}

// 检查用户名
async function checkUsername() {
  if (!registerInfo.value.username) {
    usernameError.value = ''
    return
  }

  if (registerInfo.value.username.length < 3) {
    usernameError.value = '用户名至少3个字符'
    return
  }

  try {
    const res = await api.checkUsername({ username: registerInfo.value.username })
    if (res.data.exists) {
      usernameError.value = '用户名已存在'
    } else {
      usernameError.value = ''
    }
  } catch (error) {
    console.error('检查用户名失败:', error)
  }
}

// 验证邀请码
async function validateInviteCode() {
  if (!registerInfo.value.inviteCode) {
    inviteCodeError.value = ''
    inviteCodeInfo.value = null
    return
  }

  try {
    const res = await api.validateInviteCode({ invite_code: registerInfo.value.inviteCode })
    if (res.data.valid) {
      inviteCodeInfo.value = res.data
      inviteCodeError.value = ''
    } else {
      inviteCodeInfo.value = null
      inviteCodeError.value = '邀请码无效'
    }
  } catch (error) {
    console.error('验证邀请码失败:', error)
    inviteCodeError.value = '验证邀请码失败'
  }
}

// 发送验证码
async function sendVerificationCode() {
  if (!canSendCode.value) return

  try {
    sendCodeLoading.value = true
    const res = await api.sendVerificationCode({
      email: registerInfo.value.email,
      code_type: 'register'
    })

    if (res.code === 200) {
      $message.success('验证码发送成功，请查收邮件')
      startCountdown()
    } else {
      $message.error(res.msg || '发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    $message.error('发送失败，请稍后重试')
  } finally {
    sendCodeLoading.value = false
  }
}

// 开始倒计时
function startCountdown() {
  sendCodeCountdown.value = 60
  const timer = setInterval(() => {
    sendCodeCountdown.value--
    if (sendCodeCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 注册
async function handleRegister() {
  // 表单验证
  if (!registerInfo.value.email) {
    $message.warning('请输入邮箱地址')
    return
  }
  if (!registerInfo.value.username) {
    $message.warning('请输入用户名')
    return
  }
  if (!registerInfo.value.password) {
    $message.warning('请输入密码')
    return
  }
  if (registerInfo.value.password.length < 6) {
    $message.warning('密码至少6位')
    return
  }
  if (registerInfo.value.password !== registerInfo.value.confirmPassword) {
    $message.warning('两次输入的密码不一致')
    return
  }
  if (!registerInfo.value.verificationCode) {
    $message.warning('请输入邮箱验证码')
    return
  }
  if (emailError.value || usernameError.value) {
    $message.warning('请先解决表单错误')
    return
  }

  try {
    registerLoading.value = true
    $message.loading('注册中...')
    
    const requestData = {
      email: registerInfo.value.email,
      username: registerInfo.value.username,
      password: registerInfo.value.password,
      verification_code: registerInfo.value.verificationCode
    }
    
    if (registerInfo.value.inviteCode) {
      requestData.invite_code = registerInfo.value.inviteCode
    }

    const res = await api.register(requestData)
    
    if (res.code === 200) {
      $message.success('注册成功！请登录')
      router.push('/login')
    } else {
      $message.error(res.msg || '注册失败')
    }
  } catch (error) {
    console.error('注册失败:', error)
    $message.error('注册失败，请稍后重试')
  } finally {
    registerLoading.value = false
  }
}

// 跳转到登录页
function goToLogin() {
  router.push('/login')
}
</script>
