<template>
  <CommonPage title="降AIGC率" :show-footer="false">
    <!-- 积分信息卡片 -->
    <div class="mb-6">
      <NCard>
        <template #header>
          <div class="flex items-center gap-2">
            <TheIcon icon="mdi:coin" :size="20" color="#f59e0b" />
            <span>积分信息(一字符消耗一积分)</span>
          </div>
        </template>
   
          <div>
            剩余积分: {{ userCredit.remaining_credits }}
            &nbsp;
            已消耗积分: {{ userCredit.consumed_credits }}
          </div>
     
      </NCard>
    </div>

    <!-- 功能选择卡片 -->
    <div class="mb-6">
      <NGrid :cols="2" :x-gap="16" :y-gap="16" responsive="screen">
        <NGridItem v-for="card in featureCards" :key="card.key">
          <NCard :class="[
            'cursor-pointer border-2 transition-all duration-300 hover:shadow-lg',
            activeTab === card.key
              ? 'border-primary bg-blue-50 dark:bg-slate-700 shadow-md'
              : 'border-transparent hover:border-gray-300 dark:hover:border-gray-600'
          ]" @click="activeTab = card.key">
            <div class="p-6">
              <div class="flex items-center gap-3 mb-4">
                <TheIcon :icon="card.icon" :size="28" :color="card.color" />
                <span class="text-lg font-semibold flex-1 text-gray-800 dark:text-gray-200">{{ card.title }}</span>
                <TheIcon v-if="activeTab === card.key" icon="mdi:check-circle" :size="20" color="#10b981" />
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                {{ card.description }}
              </div>
            </div>
          </NCard>
        </NGridItem>
      </NGrid>
    </div>

    <!-- 语言和平台选择 -->
    <div class="mb-5 p-4 bg-gray-50 dark:bg-slate-800 border border-gray-200 dark:border-slate-600 rounded-lg">
      <NSpace align="center" :size="16" :wrap="false">
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">语言：</span>
        <NSelect v-model:value="language" :options="languageOptions" size="medium" style="width: 120px" />
        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">平台：</span>
        <NSelect v-model:value="platform" :options="platformOptions" size="medium" style="width: 120px" />
        <span class="text-sm text-amber-600 dark:text-amber-400 ml-4">
          知网只有官方入口 cx.cnki.net，淘宝、拼多多等购买的不具参考意义
        </span>
      </NSpace>
    </div>

    <!-- 主要内容区域 -->
    <div class="space-y-5">
      <NCard :title="activeTab === 'deai' ? '降AIGC率' : activeTab === 'rewrite' ? '降重复率' : '降重+降AIGC'">
        <template #header-extra>
          <NAlert type="warning" :show-icon="false" class="text-sm">
            为保护用户内容安全，检测处理的结果不会保存，请及时复制到自己的文件中。
          </NAlert>
        </template>

        <div class="grid lg:grid-cols-2 gap-6 mt-4">
          <div class="space-y-3">
            <NInput v-model:value="inputText" type="textarea" :placeholder="`输入或粘贴文档（20-${maxChars}个字符）`" :rows="12"
              :maxlength="maxChars" show-count clearable />
            <div class="flex justify-between items-center">
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ charCount }}/{{ maxChars }} 字符</span>
              <NSpace :size="12">
                <NButton size="medium" @click="resetText">重置</NButton>
                <NButton type="primary" size="medium" :loading="isProcessing" @click="processText">
                  一键生成
                </NButton>
              </NSpace>
            </div>
          </div>
          <div class="space-y-3 flex flex-col">
            <NInput v-model:value="outputText" type="textarea" :rows="12" :disabled="true" />
            <NButton v-if="outputText" type="primary" size="medium" @click="copyResult">
              复制结果
            </NButton>
          </div>
        </div>
      </NCard>

      <!-- 文件上传区域 -->
      <NCard>
        <NUpload ref="uploadRef" v-model:file-list="fileList" :default-upload="false" :disabled="isUploading" :max="1"
          accept=".docx">
          <NUploadDragger>
            <div class="flex flex-col items-center gap-4 py-12 px-12 text-center">
              <TheIcon :icon="isUploading ? 'mdi:loading' : 'mdi:cloud-upload-outline'" :size="48"
                color="#E25D33" :class="{ 'animate-spin': isUploading }" />
              <div class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                一键全文降重（{{languageOptions.find(l => l.value === language).label}},{{platformOptions.find(p => p.value
                ===
                platform).label }}）
              </div>

              <div class="space-y-3">
                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {{ isUploading ? '处理中...' : '点击或者拖动文件来上传' }}
                </div>

                <div class="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  仅支持DOCX格式的文件，DOC文件请先在Word中转换为DOCX（"另存为"->"Word文档(.docx)"）。
                </div>

                <div class="text-sm text-amber-600 dark:text-amber-400 leading-relaxed">
                  不支持打开密码、批注模式，请先接受或拒绝所有修订，并关闭所有批注，否则这些部分不会被处理。
                </div>
              </div>
            </div>
          </NUploadDragger>
        </NUpload>

        <div class="flex justify-center mt-4">
          <NButton size="large" type="primary" :disabled="isUploading || fileList.length === 0"
            @click="handleFileUpload">
            <template #icon>
              <TheIcon icon="mdi:file-upload-outline" />
            </template>
            开始处理
          </NButton>
        </div>
      </NCard>
    </div>

    <!-- 处理记录 -->
    <NCard class="mt-6" title="处理记录">
      <NSpin :show="recordsLoading">
        <NDataTable
          :columns="columns"
          :data="processRecords"
          :bordered="false"
          :single-line="false"
          :pagination="false"
          :loading="recordsLoading"
          empty-description="暂无处理记录"
          size="medium"
        />

        <!-- 分页 -->
        <div class="flex justify-center mt-4" v-if="pagination.total > 0">
          <NPagination v-model:page="pagination.page" :page-count="pagination.totalPages"
            :page-size="pagination.pageSize" :item-count="pagination.total" show-size-picker show-quick-jumper
            @update:page="handlePageChange" />
        </div>
      </NSpin>
    </NCard>
  </CommonPage>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, h } from 'vue'
import CommonPage from '@/components/page/CommonPage.vue'
import {
  NButton,
  NCard,
  NInput,
  NSpace,
  NGrid,
  NGridItem,
  NAlert,
  NSpin,
  NUpload,
  NUploadDragger,
  NSelect,
  NDataTable,
  NPagination,
  NTag,
  NTime,
  useMessage,
} from 'naive-ui'

import TheIcon from '@/components/icon/TheIcon.vue'
import api from '@/api'

defineOptions({ name: '降AIGC率' })

const message = useMessage()

// 定时器相关
const pollingTimer = ref(null)
const POLLING_INTERVAL = 300000 // 300秒轮询一次

// 当前选中的功能
const activeTab = ref('deai')

// 文本输入相关
const inputText = ref('')
const outputText = ref('')
const isProcessing = ref(false)

// 积分相关
const userCredit = ref({
  consumed_credits: 0,
  remaining_credits: 0
})
const creditLoading = ref(false)

// 语言选择
const language = ref('Chinese')
const languageOptions = [
  { label: '中文', value: 'Chinese' },
  { label: 'English', value: 'English' }
]

// 平台选择
const platform = ref('zhiwang')
const platformOptions = [
  { label: '知网', value: 'zhiwang' },
  { label: '维普', value: 'weipu' },
  { label: '格子达', value: 'gezida' }
]

// 字符计数
const charCount = computed(() => inputText.value.length)
const maxChars = 5000

// 功能卡片配置
const featureCards = [
  {
    key: 'deai',
    title: '降AIGC率',
    icon: 'mdi:robot-outline',
    description: '优化文本内容，降低AI生成痕迹，使文本更具人文特色（修改文本大小，对重复率基本不会形成影响）',
    color: '#6366f1'
  },
  {
    key: 'rewrite', 
    title: '降重复率',
    icon: 'mdi:refresh',
    description: '智能分析文本并提供多样化的改写选项，有效降低文本重复率（注意可能增加AI痕迹），降低不能错',
    color: '#f59e0b'
  },
  // {
  //   key: 'combo',
  //   title: '降重+降AIGC',
  //   icon: 'mdi:lightning-bolt',
  //   description: '同时降低文本重复率和AI生成痕迹，省去已先降重再降AIGC的麻烦（需要2倍点数）',
  //   color: '#10b981'
  // },
  // {
  //   key: 'polish',
  //   title: '学术润色',
  //   icon: 'mdi:pencil-outline',
  //   description: '提升文章的学术表达，优化专业术语使用，提高文章质量（有可能导致效果上升，目前仍在测试阶段）',
  //   color: '#8b5cf6'
  // }
]

// 处理文本
const processText = async () => {
  if (!inputText.value.trim()) {
    message.warning('请输入需要处理的文本')
    return
  }

  if (inputText.value.length > maxChars) {
    message.warning(`文本长度不能超过${maxChars}字符`)
    return
  }

  isProcessing.value = true

  try {
    let result
    const requestData = {
      text: inputText.value,
      lang: language.value,
      type: platform.value
    }

    if (activeTab.value === 'deai') {
      result = await api.deaiText(requestData)
    } else if (activeTab.value === 'rewrite') {
      result = await api.rewriteText(requestData)
    } else {
      // 组合功能暂时使用降AIGC接口
      result = await api.deaiText(requestData)
    }

    if (result.code === 200) {
      outputText.value = result.data.text
      message.success(result.msg)
      // 刷新积分信息
      loadUserCredit()
    } else {
      message.error(result.msg || '处理失败')
    }
  } catch (error) {
    console.error('Processing error:', error)
    message.error('处理失败，请稍后重试')
  } finally {
    isProcessing.value = false
  }
}

// 重置
const resetText = () => {
  inputText.value = ''
  outputText.value = ''
}

// 复制结果
const copyResult = async () => {
  if (!outputText.value) {
    message.warning('暂无内容可复制')
    return
  }

  try {
    // 优先使用现代 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(outputText.value)
      message.success('复制成功')
    } else {
      // 降级到传统方法
      const textArea = document.createElement('textarea')
      textArea.value = outputText.value
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()
      
      if (document.execCommand('copy')) {
        message.success('复制成功')
      } else {
        message.error('复制失败，请手动复制')
      }
      
      document.body.removeChild(textArea)
    }
  } catch (error) {
    console.log(error)
    message.error('复制失败，请手动复制')
  }
}

// 文件上传处理
const uploadRef = ref(null)
const isUploading = ref(false)
const userDocId = ref('')
const fileList = ref([])

// 处理记录相关
const processRecords = ref([])
const recordsLoading = ref(false)
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
})

// 表格列定义
const columns = [
  {
    title: '文件名',
    key: 'file_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '处理模式',
    key: 'process_mode',
    width: 120,
    render(row) {
      return h(NTag, {
        type: row.process_mode === 'deai' ? 'info' : 'warning'
      }, {
        default: () => row.process_mode === 'deai' ? '降AIGC率' : '降重复率'
      })
    }
  },
  {
    title: '处理类型',
    key: 'process_type',
    width: 100,
    render(row) {
      const typeMap = {
        'zhiwang': '知网',
        'weipu': '维普',
        'gezida': '格子达'
      }
      return typeMap[row.process_type] || row.process_type
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusConfig = {
        'completed': { type: 'success', text: '已完成' },
        'processing': { type: 'info', text: '处理中' },
        'failed': { type: 'error', text: '失败' }
      }
      const config = statusConfig[row.status] || { type: 'default', text: row.status }
      return h(NTag, {
        type: config.type
      }, {
        default: () => config.text
      })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return h(NTime, {
        time: new Date(row.created_at),
        format: 'yyyy-MM-dd HH:mm:ss'
      })
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 100,
    render(row) {
      if (row.status === 'completed') {
        return h(NButton, {
          type: 'primary',
          size: 'small',
          onClick: () => downloadDocument(row.user_doc_id, row.id)
        }, {
          default: () => '下载'
        })
      } else {
        return h('span', { class: 'text-gray-400' }, '-')
      }
    }
  }
]

// 文件上传处理（使用fileList）
const handleFileUpload = async () => {
  console.log('当前文件列表:', fileList.value)

  // 检查是否有文件
  if (!fileList.value || fileList.value.length === 0) {
    message.error('请先选择文件')
    return
  }

  // 获取第一个文件（因为max=1，只能有一个文件）
  const fileItem = fileList.value[0]

  // 检查文件状态
  if (fileItem.status === 'error') {
    message.error('文件上传出错，请重新选择')
    return
  }

  // 检查文件对象是否存在
  if (!fileItem.file) {
    message.error('文件对象不存在')
    return
  }

  // 检查文件格式
  if (!fileItem.name.endsWith('.docx')) {
    message.error('仅支持 .docx 格式文件')
    return
  }

  // 检查文件大小 (10MB)
  if (fileItem.file.size > 10 * 1024 * 1024) {
    message.error('文件大小不能超过 10MB')
    return
  }

  // 更新文件状态为上传中
  fileItem.status = 'uploading'
  fileItem.percentage = 0

  isUploading.value = true

  try {
    // 准备表单数据
    const formData = new FormData()
    formData.append('file', fileItem.file)
    formData.append('mode', activeTab.value === 'deai' ? 'deai' : 'rewrite')
    formData.append('type_', platform.value)
    formData.append('changed_only', 'true')
    formData.append('skip_english', 'false')
    formData.append('language', language.value)

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (fileItem.percentage < 90) {
        fileItem.percentage += 10
      }
    }, 200)

    // 上传文件
    const result = await api.uploadDocument(formData)

    // 清除进度模拟
    clearInterval(progressInterval)

    if (result.code === 200) {
      userDocId.value = result.data.user_doc_id
      fileItem.status = 'finished'
      fileItem.percentage = 100
      message.success(result.msg)

      // 刷新处理记录列表
      await loadProcessRecords()
      // 重启定时器以便及时获取处理状态更新
      startPolling()
    } else {
      fileItem.status = 'error'
      fileItem.percentage = null
      message.error(result.msg || '文件上传失败')
    }
  } catch (error) {
    console.error('Upload error:', error)
    fileItem.status = 'error'
    fileItem.percentage = null
    message.error('文件上传失败，请稍后重试')
  } finally {
    isUploading.value = false
  }
}





// 加载用户积分信息
const loadUserCredit = async () => {
  creditLoading.value = true
  try {
    const result = await api.getUserCreditInfo()
    if (result.code === 200) {
      userCredit.value = result.data
    }
  } catch (error) {
    console.error('Load credit error:', error)
  } finally {
    creditLoading.value = false
  }
}

// 加载处理记录
const loadProcessRecords = async (showLoading = true) => {
  if (showLoading) {
    recordsLoading.value = true
  }
  try {
    const result = await api.getProcessRecords({
      page: pagination.value.page,
      page_size: pagination.value.pageSize,
      process_mode: activeTab.value
    })

    if (result.code === 200) {
      processRecords.value = result.data.items
      pagination.value.total = result.data.total
      pagination.value.totalPages = result.data.total_pages
    }
  } catch (error) {
    console.error('Load records error:', error)
    if (showLoading) {
      message.error('加载记录失败')
    }
  } finally {
    if (showLoading) {
      recordsLoading.value = false
    }
  }
}

// 启动定时器轮询
const startPolling = () => {
  // 清除已存在的定时器
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }

  // 启动新的定时器
  pollingTimer.value = setInterval(() => {
    // 静默刷新，不显示loading状态
    loadProcessRecords(false)
  }, POLLING_INTERVAL)
}

// 停止定时器轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

// 分页改变
const handlePageChange = (page) => {
  pagination.value.page = page
  loadProcessRecords()
}

// 下载文档
const downloadDocument = async (docId, recordId = null) => {
  try {
    const response = await api.downloadDocument({
      user_doc_id: docId,
      file_name: '处理后的文档'
    })

    // 从响应头中获取文件名
    let filename = '处理后的文档.docx'
    const contentDisposition = response.headers?.['content-disposition']
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/)
      if (filenameMatch) {
        filename = decodeURIComponent(filenameMatch[1])
      }
    }

    // 创建下载链接
    const blob = new Blob([response.data || response], {
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    // 记录下载次数
    if (recordId) {
      await api.recordDownload(recordId)
    }

    message.success('文档下载成功')

    // 刷新记录列表
    await loadProcessRecords()
  } catch (error) {
    console.error('Download error:', error)
    message.error('文档下载失败')
  }
}

// 页面初始化
onMounted(() => {
  loadUserCredit()
  loadProcessRecords()
  // 启动定时器轮询
  startPolling()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopPolling()
})

// 监听activeTab变化，重新加载记录并重启定时器
watch(activeTab, () => {
  pagination.value.page = 1
  loadProcessRecords()
  // 重启定时器以适应新的activeTab
  startPolling()
})


</script>



<style scoped>
/* 全局字体优化 */
* {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 确保最小字体大小 */
.text-xs {
  font-size: 13px !important;
}

.text-sm {
  font-size: 14px !important;
}

.text-base {
  font-size: 16px !important;
}

.text-lg {
  font-size: 18px !important;
}

/* Naive UI 组件字体优化 */
:deep(.n-button) {
  font-size: 14px !important;
}

:deep(.n-button--small-type) {
  font-size: 13px !important;
}

:deep(.n-button--medium-type) {
  font-size: 14px !important;
}

:deep(.n-button--large-type) {
  font-size: 16px !important;
}

:deep(.n-select) {
  font-size: 14px !important;
}

:deep(.n-input) {
  font-size: 14px !important;
}

:deep(.n-card .n-card-header__main) {
  font-size: 16px !important;
  font-weight: 600;
}

/* 滚动条样式优化 */
.h-80::-webkit-scrollbar {
  width: 6px;
}

.h-80::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark .h-80::-webkit-scrollbar-track {
  background: #1e293b;
}

.h-80::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dark .h-80::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.h-80::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark .h-80::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 响应式网格适配 */
@media (max-width: 1024px) {
  .grid.lg\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.n-grid[responsive="screen"]) {
    grid-template-columns: repeat(2, 1fr) !important;
  }
  
  .mb-5 :deep(.n-space) {
    flex-wrap: wrap !important;
    gap: 12px !important;
  }
  
  /* 移动端字体调整 */
  .text-base {
    font-size: 15px;
  }
  
  .text-sm {
    font-size: 13px;
  }
}

@media (max-width: 640px) {
  :deep(.n-grid[responsive="screen"]) {
    grid-template-columns: 1fr !important;
  }
  
  .mb-5 {
    padding: 16px;
  }
  
  .mb-5 :deep(.n-space) {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 16px !important;
  }
  
  .text-sm.text-amber-600 {
    margin-left: 0 !important;
    margin-top: 8px;
  }
  
  /* 移动端输出区域高度调整 */
  .h-80 {
    height: 16rem;
  }
  
  /* 移动端字体进一步调整 */
  .text-lg {
    font-size: 17px;
  }
  
  .text-base {
    font-size: 14px;
  }
  
  /* 移动端上传区域优化 */
  .py-12 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  
  .px-12 {
    padding-left: 1.5rem !important;
    padding-right: 1.5rem !important;
  }
  
  .max-w-2xl {
    max-width: 100% !important;
  }
}

/* 卡片悬停效果增强 */
.cursor-pointer:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* 上传区域悬停效果 */
:deep(.n-upload-dragger) {
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
  min-height: 200px;
}

:deep(.n-upload-dragger:hover) {
  border-color: #E25D33;
  /* background-color: #aeb4ba; */
}

.dark :deep(.n-upload-dragger) {
  border-color: #4b5563;
}

.dark :deep(.n-upload-dragger:hover) {
  border-color: #E25D33;
  background-color: #1e293b;
}

/* 上传区域文字排版优化 */
.upload-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.upload-text-container .space-y-3 > * + * {
  margin-top: 0.75rem;
}

/* 确保文字不会挤在一起 */
.upload-description {
  line-height: 1.6;
  word-spacing: 0.1em;
}

/* 按钮样式优化 */
:deep(.n-button) {
  transition: all 0.2s ease;
}

:deep(.n-button:hover) {
  transform: translateY(-1px);
}

.n-button--primary-type.n-button--small-type {
  padding: 0 12px;
  height: 28px;
}

.n-button--tiny-type {
  padding: 0 8px;
  height: 24px;
  font-size: 11px;
}

/* 输入框聚焦效果 */
:deep(.n-input) {
  transition: all 0.2s ease;
}

/* 卡片阴影效果 */
:deep(.n-card) {
  transition: box-shadow 0.3s ease;
}

/* 加载状态优化 */
:deep(.n-spin-container) {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 选择器下拉菜单样式 */
:deep(.n-select) {
  transition: all 0.2s ease;
}

/* 提示文字动画 */
.text-xs {
  transition: color 0.2s ease;
}

/* 网格间距优化 */
:deep(.n-grid-item) {
  min-height: 120px;
}

/* 确保文本不会溢出 */
.whitespace-pre-wrap {
  word-wrap: break-word;
  word-break: break-word;
}
</style>
