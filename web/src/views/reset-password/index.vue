<template>
  <AppPage :show-footer="true" bg-cover :style="{ backgroundImage: `url(${bgImg})` }">
    <div
      style="transform: translateY(25px)"
      class="m-auto max-w-1500 min-w-345 f-c-c rounded-10 bg-white bg-opacity-60 p-15 card-shadow"
      dark:bg-dark
    >
      <div hidden w-380 px-20 py-35 md:block>
        <icon-custom-front-page pt-10 text-300 color-primary></icon-custom-front-page>
      </div>

      <div w-320 flex-col px-20 py-35>
        <h5 f-c-c text-24 font-normal color="#6a6a6a">
          <icon-custom-logo mr-10 text-50 color-primary />{{ $t('app_name') }} - 重置密码
        </h5>
        
        <div mt-30>
          <n-input
            v-model:value="resetInfo.email"
            class="h-50 items-center pl-10 text-16"
            placeholder="请输入注册邮箱地址"
            :maxlength="255"
            @blur="checkEmail"
          />
          <div v-if="emailError" class="text-red-500 text-12 mt-5">{{ emailError }}</div>
        </div>

        <div mt-20>
          <div class="flex items-center gap-10">
            <n-input
              v-model:value="resetInfo.verificationCode"
              class="h-50 items-center pl-10 text-16 flex-1"
              placeholder="请输入邮箱验证码"
              :maxlength="6"
            />
            <n-button
              class="h-50 w-120"
              :disabled="!canSendCode || sendCodeLoading"
              :loading="sendCodeLoading"
              @click="sendVerificationCode"
            >
              {{ sendCodeText }}
            </n-button>
          </div>
        </div>

        <div mt-20>
          <n-input
            v-model:value="resetInfo.newPassword"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="请输入新密码（至少6位）"
            :maxlength="50"
          />
        </div>

        <div mt-20>
          <n-input
            v-model:value="resetInfo.confirmPassword"
            class="h-50 items-center pl-10 text-16"
            type="password"
            show-password-on="mousedown"
            placeholder="请确认新密码"
            :maxlength="50"
          />
        </div>

        <div mt-20>
          <n-button
            h-50
            w-full
            rounded-5
            text-16
            type="primary"
            :loading="resetLoading"
            @click="handleResetPassword"
          >
            重置密码
          </n-button>
        </div>

        <div mt-20 text-center>
          <n-button text @click="goToLogin">
            返回登录
          </n-button>
          <span class="mx-10">|</span>
          <n-button text @click="goToRegister">
            注册账户
          </n-button>
        </div>
      </div>
    </div>
  </AppPage>
</template>

<script setup>
import bgImg from '@/assets/images/login_bg.webp'
import api from '@/api'

const router = useRouter()

const resetInfo = ref({
  email: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
})

const emailError = ref('')
const resetLoading = ref(false)
const sendCodeLoading = ref(false)
const sendCodeCountdown = ref(0)

// 发送验证码按钮文本
const sendCodeText = computed(() => {
  if (sendCodeCountdown.value > 0) {
    return `${sendCodeCountdown.value}s后重发`
  }
  return '发送验证码'
})

// 是否可以发送验证码
const canSendCode = computed(() => {
  return resetInfo.value.email && 
         !emailError.value && 
         sendCodeCountdown.value === 0
})

// 检查邮箱
async function checkEmail() {
  if (!resetInfo.value.email) {
    emailError.value = ''
    return
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(resetInfo.value.email)) {
    emailError.value = '请输入有效的邮箱地址'
    return
  }

  try {
    const res = await api.checkEmail({ email: resetInfo.value.email })
    if (!res.data.exists) {
      emailError.value = '该邮箱未注册，请先注册账户'
    } else {
      emailError.value = ''
    }
  } catch (error) {
    console.error('检查邮箱失败:', error)
  }
}

// 发送验证码
async function sendVerificationCode() {
  if (!canSendCode.value) return

  try {
    sendCodeLoading.value = true
    const res = await api.sendVerificationCode({
      email: resetInfo.value.email,
      code_type: 'reset_password'
    })

    if (res.code === 200) {
      $message.success('验证码发送成功，请查收邮件')
      startCountdown()
    } else {
      $message.error(res.msg || '发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    $message.error('发送失败，请稍后重试')
  } finally {
    sendCodeLoading.value = false
  }
}

// 开始倒计时
function startCountdown() {
  sendCodeCountdown.value = 60
  const timer = setInterval(() => {
    sendCodeCountdown.value--
    if (sendCodeCountdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 重置密码
async function handleResetPassword() {
  // 表单验证
  if (!resetInfo.value.email) {
    $message.warning('请输入邮箱地址')
    return
  }
  if (!resetInfo.value.verificationCode) {
    $message.warning('请输入邮箱验证码')
    return
  }
  if (!resetInfo.value.newPassword) {
    $message.warning('请输入新密码')
    return
  }
  if (resetInfo.value.newPassword.length < 6) {
    $message.warning('密码至少6位')
    return
  }
  if (resetInfo.value.newPassword !== resetInfo.value.confirmPassword) {
    $message.warning('两次输入的密码不一致')
    return
  }
  if (emailError.value) {
    $message.warning('请先解决表单错误')
    return
  }

  try {
    resetLoading.value = true
    $message.loading('重置密码中...')
    
    const res = await api.resetPassword({
      email: resetInfo.value.email,
      verification_code: resetInfo.value.verificationCode,
      new_password: resetInfo.value.newPassword
    })
    
    if (res.code === 200) {
      $message.success('密码重置成功！请使用新密码登录')
      router.push('/login')
    } else {
      $message.error(res.msg || '重置失败')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    $message.error('重置失败，请稍后重试')
  } finally {
    resetLoading.value = false
  }
}

// 跳转到登录页
function goToLogin() {
  router.push('/login')
}

// 跳转到注册页
function goToRegister() {
  router.push('/register')
}
</script>
