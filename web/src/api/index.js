import { request } from '@/utils'

export default {
  login: (data) => request.post('/base/access_token', data, { noNeedToken: true }),
  getUserInfo: () => request.get('/base/userinfo'),
  getUserMenu: () => request.get('/base/usermenu'),
  getUserApi: () => request.get('/base/userapi'),
  // profile
  updatePassword: (data = {}) => request.post('/base/update_password', data),
  // auth
  sendVerificationCode: (data = {}) => request.post('/auth/send_verification_code', data, { noNeedToken: true }),
  verifyCode: (data = {}) => request.post('/auth/verify_code', data, { noNeedToken: true }),
  register: (data = {}) => request.post('/auth/register', data, { noNeedToken: true }),
  resetPassword: (data = {}) => request.post('/auth/reset_password', data, { noNeedToken: true }),
  checkEmail: (params = {}) => request.get('/auth/check_email', { params, noNeedToken: true }),
  checkUsername: (params = {}) => request.get('/auth/check_username', { params, noNeedToken: true }),
  validateInviteCode: (params = {}) => request.get('/invites/validate_invite_code', { params, noNeedToken: true }),
  // users
  getUserList: (params = {}) => request.get('/user/list', { params }),
  getUserById: (params = {}) => request.get('/user/get', { params }),
  createUser: (data = {}) => request.post('/user/create', data),
  updateUser: (data = {}) => request.post('/user/update', data),
  deleteUser: (params = {}) => request.delete(`/user/delete`, { params }),
  resetPassword: (data = {}) => request.post(`/user/reset_password`, data),
  // role
  getRoleList: (params = {}) => request.get('/role/list', { params }),
  createRole: (data = {}) => request.post('/role/create', data),
  updateRole: (data = {}) => request.post('/role/update', data),
  deleteRole: (params = {}) => request.delete('/role/delete', { params }),
  updateRoleAuthorized: (data = {}) => request.post('/role/authorized', data),
  getRoleAuthorized: (params = {}) => request.get('/role/authorized', { params }),
  // menus
  getMenus: (params = {}) => request.get('/menu/list', { params }),
  createMenu: (data = {}) => request.post('/menu/create', data),
  updateMenu: (data = {}) => request.post('/menu/update', data),
  deleteMenu: (params = {}) => request.delete('/menu/delete', { params }),
  // apis
  getApis: (params = {}) => request.get('/api/list', { params }),
  createApi: (data = {}) => request.post('/api/create', data),
  updateApi: (data = {}) => request.post('/api/update', data),
  deleteApi: (params = {}) => request.delete('/api/delete', { params }),
  refreshApi: (data = {}) => request.post('/api/refresh', data),
  // depts
  getDepts: (params = {}) => request.get('/dept/list', { params }),
  createDept: (data = {}) => request.post('/dept/create', data),
  updateDept: (data = {}) => request.post('/dept/update', data),
  deleteDept: (params = {}) => request.delete('/dept/delete', { params }),
  // auditlog
  getAuditLogList: (params = {}) => request.get('/auditlog/list', { params }),
  // humanizer
  deaiText: (data = {}) => request.post('/humanizer/deai', data),
  rewriteText: (data = {}) => request.post('/humanizer/rewrite', data),
  uploadDocument: (formData) => request.post('/humanizer/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  checkDocumentStatus: (data = {}) => request.post('/humanizer/status', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  downloadDocument: (data = {}) => request.post('/humanizer/download', data, {
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    }
  }),
  // process records
  getProcessRecords: (params = {}) => request.get('/process_records/list', { params }),
  getProcessStats: () => request.get('/process_records/stats'),
  recordDownload: (recordId) => request.post(`/process_records/${recordId}/download`),
  // user credits
  getUserCreditInfo: () => request.get('/user_credits/info'),
  rechargeCredits: (data = {}) => request.post('/user_credits/recharge', data),
  getCreditTransactions: (params = {}) => request.get('/user_credits/transactions', { params }),
  adminRechargeCredits: (data = {}) => request.post('/user_credits/admin/recharge', data),
  // invites
  getMyInviteData: () => request.get('/invites/my_invite_data'),
  getMyInviteRecords: (params = {}) => request.get('/invites/my_invite_records', { params }),
  getMyInviteStatistics: () => request.get('/invites/my_invite_statistics'),
}
