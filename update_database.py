#!/usr/bin/env python3
"""
数据库更新脚本
用于添加用户注册、邮箱验证、密码重置和推广功能相关的表结构
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tortoise import Tortoise
from app.settings.config import settings
from app.models.admin import User, EmailVerification, InviteRecord
from app.utils.verification import verification_service
from app.log import logger


async def update_user_table():
    """更新用户表，添加新字段"""
    try:
        # 检查是否需要添加新字段
        # 这里使用简单的方式，实际生产环境建议使用正式的数据库迁移工具
        
        # 为现有用户生成邀请码
        users_without_invite_code = await User.filter(invite_code__isnull=True)
        
        for user in users_without_invite_code:
            if not user.invite_code:
                invite_code = await verification_service.get_unique_invite_code()
                user.invite_code = invite_code
                await user.save()
                logger.info(f"为用户 {user.username} 生成邀请码: {invite_code}")
        
        logger.info("用户表更新完成")
        
    except Exception as e:
        logger.error(f"更新用户表失败: {str(e)}")
        raise


async def create_new_tables():
    """创建新的表"""
    try:
        # Tortoise ORM 会自动创建表，这里只是确保表存在
        await EmailVerification.all().limit(1)
        logger.info("邮箱验证表检查完成")
        
        await InviteRecord.all().limit(1)
        logger.info("邀请记录表检查完成")
        
    except Exception as e:
        logger.error(f"创建新表失败: {str(e)}")
        raise


async def main():
    """主函数"""
    try:
        # 初始化数据库连接
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 生成数据库表
        await Tortoise.generate_schemas()
        
        logger.info("开始更新数据库...")
        
        # 更新用户表
        await update_user_table()
        
        # 创建新表
        await create_new_tables()
        
        logger.info("数据库更新完成！")
        
    except Exception as e:
        logger.error(f"数据库更新失败: {str(e)}")
        sys.exit(1)
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    print("正在更新数据库...")
    print("添加用户注册、邮箱验证、密码重置和推广功能相关的表结构...")
    
    asyncio.run(main())
    
    print("数据库更新完成！")
    print("\n新增功能:")
    print("1. 用户注册功能（邮箱验证）")
    print("2. 密码重置功能")
    print("3. 推广功能（邀请码和积分奖励）")
    print("4. 邮箱验证码系统")
    print("\n请配置邮件服务器设置:")
    print("- SMTP_HOST: SMTP服务器地址")
    print("- SMTP_PORT: SMTP端口")
    print("- SMTP_USER: 发送邮件的邮箱")
    print("- SMTP_PASSWORD: 邮箱授权码")
    print("\n前端新增页面:")
    print("- /register - 用户注册页面")
    print("- /reset-password - 密码重置页面")
    print("- /invite - 推广中心页面（需要登录）")
