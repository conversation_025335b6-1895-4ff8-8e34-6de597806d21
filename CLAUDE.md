# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 FastAPI + Vue3 + Naive UI 的现代化前后端分离开发平台，融合了 RBAC 权限管理、动态路由和 JWT 鉴权。

## 开发环境设置

### 后端 (Python 3.11+)

**推荐方式（使用 uv）：**
```bash
# 安装 uv
pip install uv

# 创建并激活虚拟环境
uv venv
source .venv/bin/activate  # Linux/Mac
# 或 .\.venv\Scripts\activate  # Windows

# 安装依赖
uv add pyproject.toml

# 启动服务
python run.py
```

**传统方式（使用 pip）：**
```bash
# 创建虚拟环境
python3 -m venv venv

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或 .\venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 启动服务
python run.py
```

### 前端 (Node.js 18.8.0+)

```bash
# 进入前端目录
cd web

# 安装依赖
pnpm i  # 推荐使用 pnpm
# 或 npm i

# 启动开发服务器
pnpm dev
```

## 常用命令

### 后端开发命令

- `make start` 或 `python run.py` - 启动后端服务 (http://localhost:9999)
- `make install` - 安装依赖
- `make check` - 检查代码格式和语法
- `make format` - 格式化代码 (black + isort)
- `make lint` - 运行 ruff 代码检查
- `make test` - 运行测试套件

### 数据库命令

- `make clean-db` - 删除 migrations 文件夹和数据库文件
- `make migrate` - 生成数据库迁移文件 (aerich migrate)
- `make upgrade` - 应用数据库迁移 (aerich upgrade)

### 前端开发命令

```bash
cd web
pnpm dev          # 启动开发服务器
pnpm build        # 构建生产版本
pnpm preview      # 预览构建结果
pnpm lint         # ESLint 检查
pnpm lint:fix     # 自动修复 ESLint 问题
pnpm prettier     # 格式化代码
```

## 项目架构

### 后端架构 (FastAPI)

- **app/api/v1/** - API 接口路由，按功能模块分组 (users, roles, menus, apis, depts, auditlog)
- **app/controllers/** - 业务逻辑控制器
- **app/models/** - Tortoise ORM 数据模型
- **app/schemas/** - Pydantic 数据验证模式
- **app/core/** - 核心功能 (异常处理、中间件、依赖注入、应用初始化)
- **app/settings/** - 配置管理，支持多数据库配置 (SQLite/MySQL/PostgreSQL)
- **app/utils/** - 工具函数 (JWT、密码处理)

### 前端架构 (Vue3 + Vite)

- **src/api/** - API 接口封装
- **src/components/** - 可复用组件 (通用组件、图标、页面组件、查询栏、表格)
- **src/composables/** - Vue3 组合式函数，包含 CRUD 通用逻辑
- **src/layout/** - 页面布局组件 (头部、侧边栏、标签页)
- **src/router/** - 路由配置和守卫
- **src/store/** - Pinia 状态管理
- **src/views/** - 页面视图

### 权限系统

- **RBAC 模型**：用户 -> 角色 -> 权限 (菜单+API)
- **动态路由**：基于用户权限动态生成菜单和路由
- **JWT 鉴权**：访问令牌有效期 7 天
- **细粒度控制**：按钮级别和接口级别权限控制

## 技术栈

### 后端技术

- **FastAPI** - 高性能异步 Web 框架
- **Tortoise ORM** - 异步 ORM，支持多种数据库
- **Pydantic** - 数据验证和序列化
- **Aerich** - 数据库迁移工具
- **JWT** - 身份验证
- **Loguru** - 日志记录

### 前端技术

- **Vue 3** - 渐进式 JavaScript 框架
- **Vite** - 极速的构建工具
- **Naive UI** - Vue 3 组件库
- **Pinia** - Vue 状态管理
- **UnoCSS** - 原子化 CSS 引擎
- **Vue Router 4** - 官方路由管理器
- **Axios** - HTTP 客户端

## 代码规范

### 后端

- 使用 **Black** (line-length: 120) 进行代码格式化
- 使用 **isort** 进行导入排序 (--profile black)
- 使用 **Ruff** 进行代码检查
- Python 版本：3.11+

### 前端

- 使用 **ESLint** 进行代码检查 (@zclzone 配置)
- 使用 **Prettier** 进行代码格式化
- Node.js 版本：18.8.0+

## 数据库

- **默认**：SQLite (db.sqlite3)
- **支持**：MySQL/MariaDB, PostgreSQL, SQL Server, Oracle
- **ORM**：Tortoise ORM (异步)
- **迁移工具**：Aerich

## 服务地址

- **后端 API**: http://localhost:9999
- **API 文档**: http://localhost:9999/docs
- **前端开发**: http://localhost:3000 (vite dev)

## 默认账户

- **用户名**: admin
- **密码**: 123456