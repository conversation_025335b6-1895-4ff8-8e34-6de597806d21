# 用户注册、邮箱验证、密码重置和推广功能使用指南

## 功能概述

本次更新为系统添加了完整的用户注册、邮箱验证、密码重置和推广功能，包括：

### 1. 用户注册功能
- 邮箱注册，支持邮箱验证码验证
- 用户名唯一性检查
- 密码强度要求（至少6位）
- 支持邀请码注册

### 2. 邮箱验证功能
- 验证码有效期10分钟
- 防止频繁发送（1分钟内限制）
- 支持注册和密码重置两种类型

### 3. 密码重置功能
- 通过邮箱验证码重置密码
- 安全的密码重置流程

### 4. 推广功能
- 每个用户拥有独立的邀请码
- 邀请链接格式：`网站地址/register?aff=邀请码`
- 成功邀请奖励1000积分
- 推广数据统计和记录查看

## 部署配置

### 1. 邮件服务配置

在 `app/settings/config.py` 中配置邮件服务器设置：

```python
# 邮件配置
SMTP_HOST: str = "smtp.qq.com"  # SMTP服务器地址
SMTP_PORT: int = 587            # SMTP端口
SMTP_USER: str = "<EMAIL>"      # 发送邮件的邮箱
SMTP_PASSWORD: str = "your-auth-code"     # 邮箱授权码
SMTP_FROM_NAME: str = "AI-clear"          # 发件人名称
SMTP_USE_TLS: bool = True                 # 是否使用TLS

# 前端地址配置
FRONTEND_URL: str = "http://localhost:3000"  # 前端地址，用于生成邀请链接
```

### 2. 数据库更新

运行数据库更新脚本：

```bash
python update_database.py
```

### 3. 重启服务

更新配置后重启后端服务。

## 前端页面

### 新增页面路由

- `/register` - 用户注册页面
- `/reset-password` - 密码重置页面
- `/invite` - 推广中心页面（需要登录）

### 登录页面更新

登录页面新增了"立即注册"和"忘记密码"链接。

## API接口

### 认证相关接口

- `POST /api/v1/auth/send_verification_code` - 发送邮箱验证码
- `POST /api/v1/auth/verify_code` - 验证邮箱验证码
- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/reset_password` - 重置密码
- `GET /api/v1/auth/check_email` - 检查邮箱是否已注册
- `GET /api/v1/auth/check_username` - 检查用户名是否已存在

### 推广相关接口

- `GET /api/v1/invites/my_invite_data` - 获取我的推广数据
- `GET /api/v1/invites/my_invite_records` - 获取我的邀请记录
- `GET /api/v1/invites/my_invite_statistics` - 获取我的邀请统计
- `GET /api/v1/invites/validate_invite_code` - 验证邀请码

## 数据库变更

### 用户表 (user)
- 新增 `affman` 字段：邀请人ID
- 新增 `invite_code` 字段：用户的邀请码

### 新增表

#### 邮箱验证表 (email_verification)
- `email`: 邮箱地址
- `code`: 验证码
- `code_type`: 验证码类型（register/reset_password）
- `is_used`: 是否已使用
- `expires_at`: 过期时间

#### 邀请记录表 (invite_record)
- `inviter_id`: 邀请人ID
- `invitee_id`: 被邀请人ID
- `invite_code`: 邀请码
- `reward_credits`: 奖励积分
- `is_rewarded`: 是否已奖励

## 使用流程

### 用户注册流程

1. 用户访问 `/register` 页面
2. 输入邮箱地址，点击"发送验证码"
3. 系统发送验证码到用户邮箱
4. 用户输入验证码、用户名、密码
5. 可选填写邀请码
6. 点击注册，系统验证信息并创建账户
7. 如有邀请人，自动发放1000积分奖励

### 密码重置流程

1. 用户访问 `/reset-password` 页面
2. 输入注册邮箱，点击"发送验证码"
3. 系统发送验证码到用户邮箱
4. 用户输入验证码和新密码
5. 点击重置密码，系统更新密码

### 推广功能使用

1. 用户登录后访问 `/invite` 页面
2. 查看自己的邀请码和邀请链接
3. 分享邀请链接给朋友
4. 朋友通过邀请链接注册成功后，获得1000积分奖励
5. 在推广中心查看邀请记录和统计数据

## 注意事项

1. **邮件服务配置**：确保邮件服务器配置正确，建议使用QQ邮箱或企业邮箱
2. **验证码安全**：验证码有效期为10分钟，使用后自动失效
3. **邀请码唯一性**：系统自动生成唯一邀请码，避免冲突
4. **积分奖励**：邀请奖励在被邀请人注册成功后自动发放
5. **前端地址配置**：确保 `FRONTEND_URL` 配置正确，用于生成邀请链接

## 故障排除

### 邮件发送失败
- 检查SMTP配置是否正确
- 确认邮箱授权码是否有效
- 检查网络连接和防火墙设置

### 验证码无效
- 检查验证码是否过期（10分钟有效期）
- 确认验证码类型是否匹配
- 检查是否已经使用过该验证码

### 邀请码无效
- 确认邀请码格式正确
- 检查邀请码是否存在于数据库中
- 确认邀请人账户状态正常
