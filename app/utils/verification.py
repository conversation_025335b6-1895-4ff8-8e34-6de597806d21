import random
import string
from datetime import datetime, timedelta
from typing import Optional

from app.models.admin import EmailVerification
from app.utils.email import email_service
from app.log import logger


class VerificationService:
    def __init__(self):
        self.code_length = 6
        self.code_expire_minutes = 10

    def generate_code(self, length: int = None) -> str:
        """生成验证码"""
        if length is None:
            length = self.code_length
        return ''.join(random.choices(string.digits, k=length))

    def generate_invite_code(self, length: int = 8) -> str:
        """生成邀请码"""
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

    async def send_verification_code(self, email: str, code_type: str) -> tuple[bool, str]:
        """发送验证码"""
        try:
            # 检查是否在短时间内重复发送
            recent_code = await EmailVerification.filter(
                email=email,
                code_type=code_type,
                created_at__gte=datetime.now() - timedelta(minutes=1)
            ).first()
            
            if recent_code:
                return False, "请勿频繁发送验证码，请稍后再试"

            # 生成验证码
            code = self.generate_code()
            expires_at = datetime.now() + timedelta(minutes=self.code_expire_minutes)

            # 保存验证码到数据库
            verification = await EmailVerification.create(
                email=email,
                code=code,
                code_type=code_type,
                expires_at=expires_at
            )

            # 发送邮件
            success = await email_service.send_verification_code(email, code, code_type)
            
            if success:
                logger.info(f"验证码发送成功: {email}, 类型: {code_type}")
                return True, "验证码发送成功"
            else:
                # 发送失败，删除数据库记录
                await verification.delete()
                return False, "邮件发送失败，请稍后重试"

        except Exception as e:
            logger.error(f"发送验证码异常: {email}, 错误: {str(e)}")
            return False, "系统错误，请稍后重试"

    async def verify_code(self, email: str, code: str, code_type: str, mark_used: bool = True) -> tuple[bool, str]:
        """验证验证码"""
        try:
            # 查找有效的验证码
            verification = await EmailVerification.filter(
                email=email,
                code=code,
                code_type=code_type,
                is_used=False,
                expires_at__gt=datetime.now()
            ).first()

            if not verification:
                return False, "验证码无效或已过期"

            if mark_used:
                # 标记为已使用
                verification.is_used = True
                await verification.save()

            logger.info(f"验证码验证成功: {email}, 类型: {code_type}")
            return True, "验证成功"

        except Exception as e:
            logger.error(f"验证码验证异常: {email}, 错误: {str(e)}")
            return False, "系统错误，请稍后重试"

    async def cleanup_expired_codes(self):
        """清理过期的验证码"""
        try:
            deleted_count = await EmailVerification.filter(
                expires_at__lt=datetime.now()
            ).delete()
            logger.info(f"清理过期验证码: {deleted_count} 条")
        except Exception as e:
            logger.error(f"清理过期验证码异常: {str(e)}")

    async def get_unique_invite_code(self) -> str:
        """生成唯一的邀请码"""
        from app.models.admin import User
        
        max_attempts = 10
        for _ in range(max_attempts):
            code = self.generate_invite_code()
            # 检查是否已存在
            existing = await User.filter(invite_code=code).first()
            if not existing:
                return code
        
        # 如果10次都重复，使用更长的码
        return self.generate_invite_code(12)


# 创建全局验证服务实例
verification_service = VerificationService()
