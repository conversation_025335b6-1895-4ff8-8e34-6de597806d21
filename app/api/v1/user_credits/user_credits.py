from fastapi import APIRouter, Query
from typing import Optional

from app.controllers.user_credit import user_credit_controller, credit_transaction_controller
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.schemas import Success
from app.schemas.user_credits import (
    CreditTransactionQuery,
    CreditRechargeRequest,
    AdminRechargeRequest
)

router = APIRouter()


@router.get("/info", summary="获取用户积分信息", dependencies=[DependAuth])
async def get_user_credit_info():
    """获取当前用户的积分信息"""
    user_id = CTX_USER_ID.get()
    user_credit = await user_credit_controller.get_or_create_user_credit(user_id)
    data = await user_credit.to_dict()
    return Success(data=data)


@router.post("/recharge", summary="充值积分", dependencies=[DependAuth])
async def recharge_credits(request: CreditRechargeRequest):
    """为当前用户充值积分"""
    user_id = CTX_USER_ID.get()
    user_credit = await user_credit_controller.recharge_credits(
        user_id=user_id,
        amount=request.amount,
        description=request.description
    )
    data = await user_credit.to_dict()
    return Success(data=data, msg="积分充值成功")


@router.post("/admin/recharge", summary="管理员给用户充值积分", dependencies=[DependAuth])
async def admin_recharge_credits(request: AdminRechargeRequest):
    """管理员为指定用户充值积分"""
    user_credit = await user_credit_controller.recharge_credits(
        user_id=request.user_id,
        amount=request.amount,
        description=request.description
    )
    data = await user_credit.to_dict()
    return Success(data=data, msg="积分充值成功")


@router.get("/transactions", summary="获取积分交易记录", dependencies=[DependAuth])
async def list_credit_transactions(
    transaction_type: Optional[str] = Query(None, description="交易类型"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(10, description="每页数量", ge=1, le=100),
):
    """获取当前用户的积分交易记录"""
    user_id = CTX_USER_ID.get()
    
    query = CreditTransactionQuery(
        user_id=user_id,
        transaction_type=transaction_type,
        page=page,
        page_size=page_size
    )
    
    result = await credit_transaction_controller.get_user_transactions(user_id, query)
    
    # 转换为字典格式
    items = []
    for transaction in result["items"]:
        items.append(await transaction.to_dict())
    
    result["items"] = items
    return Success(data=result)
