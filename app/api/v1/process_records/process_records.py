from fastapi import APIRouter, Query
from typing import Optional
import httpx

from app.controllers.process_record import process_record_controller
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.schemas import Success
from app.schemas.process_records import (
    ProcessRecordQuery,
)
from app.log import logger

router = APIRouter()

# API配置
API_KEY = "sk-Z3Snt9PE2nfO5CV6yJNcVHCY"  # 建议从环境变量获取


async def check_and_update_processing_records(records):
    """检查并更新处理中的记录状态"""
    updated_records = []

    for record in records:
        record_updated = False

        # 如果记录状态是处理中，则查询最新状态
        if record.status == "processing":
            try:
                # 调用第三方API查询状态
                api_url = "https://api3.speedai.chat/v1/docx/status"

                payload = {
                    "user_doc_id": record.user_doc_id
                }

                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {API_KEY}",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                }

                async with httpx.AsyncClient(timeout=10.0) as client:
                    response = await client.post(api_url, json=payload, headers=headers)

                    if response.status_code == 200:
                        result = response.json()
                        status = result.get("status", "unknown")

                        # 如果状态发生变化，更新数据库记录
                        if status in ["completed", "error"] and status != record.status:
                            error_message = None
                            if status == "error":
                                error_message = result.get("error", "处理失败")

                            # 更新数据库记录
                            updated_record = await process_record_controller.update_record_status(
                                record.user_doc_id, status, error_message
                            )
                            if updated_record:
                                updated_records.append(updated_record)
                                record_updated = True
                                logger.info(f"更新记录状态: {record.user_doc_id} -> {status}")
                    else:
                        logger.warning(f"查询状态失败: {record.user_doc_id}, status_code: {response.status_code}")

            except Exception as e:
                logger.error(f"检查记录状态异常: {record.user_doc_id}, error: {str(e)}")

        # 如果没有更新，使用原记录
        if not record_updated:
            updated_records.append(record)

    return updated_records


@router.get("/list", summary="获取处理记录列表", dependencies=[DependAuth])
async def list_process_records(
    status: Optional[str] = Query(None, description="处理状态"),
    process_mode: Optional[str] = Query(None, description="处理模式"),
    process_type: Optional[str] = Query(None, description="处理类型"),
    page: int = Query(1, description="页码", ge=1),
    page_size: int = Query(10, description="每页数量", ge=1, le=100),
):
    """获取当前用户的处理记录列表"""
    user_id = CTX_USER_ID.get()

    query = ProcessRecordQuery(
        user_id=user_id,
        status=status,
        process_mode=process_mode,
        process_type=process_type,
        page=page,
        page_size=page_size
    )

    result = await process_record_controller.get_user_records(user_id, query)

    # 检查并更新处理中的记录状态
    updated_records = await check_and_update_processing_records(result["items"])

    # 转换为响应格式，直接使用to_dict()避免datetime序列化问题
    items = []
    for record in updated_records:
        record_dict = await record.to_dict()
        items.append(record_dict)

    response_data = {
        "items": items,
        "total": result["total"],
        "page": result["page"],
        "page_size": result["page_size"],
        "total_pages": result["total_pages"]
    }

    return Success(data=response_data)


@router.get("/stats", summary="获取处理记录统计", dependencies=[DependAuth])
async def get_process_stats():
    """获取当前用户的处理记录统计信息"""
    user_id = CTX_USER_ID.get()
    stats = await process_record_controller.get_user_stats(user_id)
    return Success(data=stats)


@router.get("/{record_id}", summary="获取处理记录详情", dependencies=[DependAuth])
async def get_process_record(record_id: int):
    """获取处理记录详情"""
    user_id = CTX_USER_ID.get()
    record = await process_record_controller.get(id=record_id)

    # 检查权限
    if record.user_id != user_id:
        return Success(code=403, msg="无权限访问")

    record_dict = await record.to_dict()

    return Success(data=record_dict)


@router.post("/{record_id}/download", summary="记录下载操作", dependencies=[DependAuth])
async def record_download(record_id: int):
    """记录下载操作，增加下载次数"""
    user_id = CTX_USER_ID.get()
    record = await process_record_controller.get(id=record_id)

    # 检查权限
    if record.user_id != user_id:
        return Success(code=403, msg="无权限访问")

    # 增加下载次数
    updated_record = await process_record_controller.increment_download_count(record.user_doc_id)

    if updated_record:
        return Success(msg="下载记录更新成功")
    else:
        return Success(code=404, msg="记录未找到")
