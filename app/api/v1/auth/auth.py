from fastapi import APIRouter, Query, HTTPException
from pydantic import EmailStr

from app.controllers.auth import auth_controller
from app.schemas.base import Success, Fail
from app.schemas.auth import (
    SendVerificationCodeRequest,
    VerifyCodeRequest,
    UserRegisterRequest,
    ResetPasswordRequest
)

router = APIRouter()


@router.post("/send_verification_code", summary="发送邮箱验证码")
async def send_verification_code(request: SendVerificationCodeRequest):
    """
    发送邮箱验证码
    - code_type: register(注册) 或 reset_password(重置密码)
    """
    success, message = await auth_controller.send_verification_code(
        request.email, 
        request.code_type
    )
    
    if success:
        return Success(msg=message)
    else:
        return Fail(msg=message)


@router.post("/verify_code", summary="验证邮箱验证码")
async def verify_code(request: VerifyCodeRequest):
    """验证邮箱验证码（不标记为已使用）"""
    success, message = await auth_controller.verify_code(
        request.email,
        request.code,
        request.code_type
    )
    
    if success:
        return Success(msg=message)
    else:
        return Fail(msg=message)


@router.post("/register", summary="用户注册")
async def register_user(request: UserRegisterRequest):
    """
    用户注册
    - 需要先发送邮箱验证码
    - 可选填写邀请码
    """
    success, message, user = await auth_controller.register_user(request)
    
    if success:
        return Success(msg=message)
    else:
        return Fail(msg=message)


@router.post("/reset_password", summary="重置密码")
async def reset_password(request: ResetPasswordRequest):
    """
    重置密码
    - 需要先发送邮箱验证码
    """
    success, message = await auth_controller.reset_password(request)
    
    if success:
        return Success(msg=message)
    else:
        return Fail(msg=message)


@router.get("/check_email", summary="检查邮箱是否已注册")
async def check_email(email: EmailStr = Query(..., description="邮箱地址")):
    """检查邮箱是否已注册"""
    exists = await auth_controller.check_email_exists(email)
    return Success(data={"exists": exists})


@router.get("/check_username", summary="检查用户名是否已存在")
async def check_username(username: str = Query(..., description="用户名")):
    """检查用户名是否已存在"""
    exists = await auth_controller.check_username_exists(username)
    return Success(data={"exists": exists})
