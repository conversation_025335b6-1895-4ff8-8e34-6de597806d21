from tortoise import fields

from app.schemas.menus import MenuType

from .base import BaseModel, TimestampMixin
from .enums import MethodType


class User(BaseModel, TimestampMixin):
    username = fields.CharField(max_length=20, unique=True, description="用户名称", index=True)
    alias = fields.CharField(max_length=30, null=True, description="姓名", index=True)
    email = fields.CharField(max_length=255, unique=True, description="邮箱", index=True)
    phone = fields.CharField(max_length=20, null=True, description="电话", index=True)
    password = fields.CharField(max_length=128, null=True, description="密码")
    is_active = fields.BooleanField(default=True, description="是否激活", index=True)
    is_superuser = fields.BooleanField(default=False, description="是否为超级管理员", index=True)
    last_login = fields.DatetimeField(null=True, description="最后登录时间", index=True)
    affman = fields.IntField(default=0, description="邀请人ID", index=True)
    invite_code = fields.CharField(max_length=20, unique=True, null=True, description="邀请码", index=True)
    roles = fields.ManyToManyField("models.Role", related_name="user_roles")
    dept_id = fields.IntField(null=True, description="部门ID", index=True)

    class Meta:
        table = "user"


class Role(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, unique=True, description="角色名称", index=True)
    desc = fields.CharField(max_length=500, null=True, description="角色描述")
    menus = fields.ManyToManyField("models.Menu", related_name="role_menus")
    apis = fields.ManyToManyField("models.Api", related_name="role_apis")

    class Meta:
        table = "role"


class Api(BaseModel, TimestampMixin):
    path = fields.CharField(max_length=100, description="API路径", index=True)
    method = fields.CharEnumField(MethodType, description="请求方法", index=True)
    summary = fields.CharField(max_length=500, description="请求简介", index=True)
    tags = fields.CharField(max_length=100, description="API标签", index=True)

    class Meta:
        table = "api"


class Menu(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, description="菜单名称", index=True)
    remark = fields.JSONField(null=True, description="保留字段")
    menu_type = fields.CharEnumField(MenuType, null=True, description="菜单类型")
    icon = fields.CharField(max_length=100, null=True, description="菜单图标")
    path = fields.CharField(max_length=100, description="菜单路径", index=True)
    order = fields.IntField(default=0, description="排序", index=True)
    parent_id = fields.IntField(default=0, description="父菜单ID", index=True)
    is_hidden = fields.BooleanField(default=False, description="是否隐藏")
    component = fields.CharField(max_length=100, description="组件")
    keepalive = fields.BooleanField(default=True, description="存活")
    redirect = fields.CharField(max_length=100, null=True, description="重定向")

    class Meta:
        table = "menu"


class Dept(BaseModel, TimestampMixin):
    name = fields.CharField(max_length=20, unique=True, description="部门名称", index=True)
    desc = fields.CharField(max_length=500, null=True, description="备注")
    is_deleted = fields.BooleanField(default=False, description="软删除标记", index=True)
    order = fields.IntField(default=0, description="排序", index=True)
    parent_id = fields.IntField(default=0, max_length=10, description="父部门ID", index=True)

    class Meta:
        table = "dept"


class DeptClosure(BaseModel, TimestampMixin):
    ancestor = fields.IntField(description="父代", index=True)
    descendant = fields.IntField(description="子代", index=True)
    level = fields.IntField(default=0, description="深度", index=True)


class AuditLog(BaseModel, TimestampMixin):
    user_id = fields.IntField(description="用户ID", index=True)
    username = fields.CharField(max_length=64, default="", description="用户名称", index=True)
    module = fields.CharField(max_length=64, default="", description="功能模块", index=True)
    summary = fields.CharField(max_length=128, default="", description="请求描述", index=True)
    method = fields.CharField(max_length=10, default="", description="请求方法", index=True)
    path = fields.CharField(max_length=255, default="", description="请求路径", index=True)
    status = fields.IntField(default=-1, description="状态码", index=True)
    response_time = fields.IntField(default=0, description="响应时间(单位ms)", index=True)
    request_args = fields.JSONField(null=True, description="请求参数")
    response_body = fields.JSONField(null=True, description="返回数据")

    class Meta:
        table = "audit_log"


class ProcessRecord(BaseModel, TimestampMixin):
    user_id = fields.IntField(description="用户ID", index=True)
    user_doc_id = fields.CharField(max_length=128, description="文档处理ID", index=True)
    status = fields.CharField(max_length=20, default="processing", description="处理状态", index=True)
    file_name = fields.CharField(max_length=255, description="原始文件名")
    file_size = fields.BigIntField(null=True, description="文件大小(字节)")
    process_mode = fields.CharField(max_length=20, description="处理模式", index=True)  # deai, rewrite
    process_type = fields.CharField(max_length=20, description="处理类型", index=True)  # zhiwang, weipu, gezida
    language = fields.CharField(max_length=20, default="Chinese", description="语言")
    error_message = fields.TextField(null=True, description="错误信息")
    download_count = fields.IntField(default=0, description="下载次数")
    last_download_at = fields.DatetimeField(null=True, description="最后下载时间")

    class Meta:
        table = "process_record"


class UserCredit(BaseModel, TimestampMixin):
    user_id = fields.IntField(unique=True, description="用户ID", index=True)
    consumed_credits = fields.BigIntField(default=0, description="已消耗积分")
    remaining_credits = fields.BigIntField(default=0, description="剩余积分")

    class Meta:
        table = "user_credit"


class CreditTransaction(BaseModel, TimestampMixin):
    user_id = fields.IntField(description="用户ID", index=True)
    transaction_type = fields.CharField(max_length=20, description="交易类型", index=True)  # consume, recharge
    amount = fields.BigIntField(description="积分数量")
    balance_before = fields.BigIntField(description="交易前余额")
    balance_after = fields.BigIntField(description="交易后余额")
    description = fields.CharField(max_length=255, description="交易描述")
    related_record_id = fields.IntField(null=True, description="关联记录ID", index=True)

    class Meta:
        table = "credit_transaction"


class EmailVerification(BaseModel, TimestampMixin):
    email = fields.CharField(max_length=255, description="邮箱地址", index=True)
    code = fields.CharField(max_length=10, description="验证码")
    code_type = fields.CharField(max_length=20, description="验证码类型", index=True)  # register, reset_password
    is_used = fields.BooleanField(default=False, description="是否已使用", index=True)
    expires_at = fields.DatetimeField(description="过期时间", index=True)

    class Meta:
        table = "email_verification"


class InviteRecord(BaseModel, TimestampMixin):
    inviter_id = fields.IntField(description="邀请人ID", index=True)
    invitee_id = fields.IntField(description="被邀请人ID", index=True)
    invite_code = fields.CharField(max_length=20, description="邀请码", index=True)
    reward_credits = fields.IntField(default=1000, description="奖励积分")
    is_rewarded = fields.BooleanField(default=False, description="是否已奖励", index=True)

    class Meta:
        table = "invite_record"
