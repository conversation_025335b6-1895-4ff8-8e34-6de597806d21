from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class BaseInviteRecord(BaseModel):
    inviter_id: int = Field(..., description="邀请人ID", example=1)
    invitee_id: int = Field(..., description="被邀请人ID", example=2)
    invite_code: str = Field(..., description="邀请码", example="ABC123")
    reward_credits: int = Field(1000, description="奖励积分", example=1000)
    is_rewarded: bool = Field(False, description="是否已奖励", example=False)


class InviteRecordCreate(BaseInviteRecord):
    pass


class InviteRecordUpdate(BaseModel):
    id: int = Field(..., description="记录ID")
    is_rewarded: Optional[bool] = Field(None, description="是否已奖励")
    reward_credits: Optional[int] = Field(None, description="奖励积分")

    def update_dict(self):
        return self.model_dump(exclude_unset=True, exclude={"id"})


class InviteRecordInfo(BaseInviteRecord):
    id: int
    created_at: datetime
    updated_at: datetime
    invitee_username: Optional[str] = None
    invitee_email: Optional[str] = None
