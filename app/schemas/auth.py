from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class SendVerificationCodeRequest(BaseModel):
    email: EmailStr = Field(..., description="邮箱地址", example="<EMAIL>")
    code_type: str = Field(..., description="验证码类型", example="register")  # register, reset_password


class VerifyCodeRequest(BaseModel):
    email: EmailStr = Field(..., description="邮箱地址", example="<EMAIL>")
    code: str = Field(..., description="验证码", example="123456")
    code_type: str = Field(..., description="验证码类型", example="register")


class UserRegisterRequest(BaseModel):
    email: EmailStr = Field(..., description="邮箱地址", example="<EMAIL>")
    username: str = Field(..., description="用户名", min_length=3, max_length=20, example="testuser")
    password: str = Field(..., description="密码", min_length=6, max_length=50, example="123456")
    verification_code: str = Field(..., description="邮箱验证码", example="123456")
    invite_code: Optional[str] = Field(None, description="邀请码", example="ABC123")


class ResetPasswordRequest(BaseModel):
    email: EmailStr = Field(..., description="邮箱地址", example="<EMAIL>")
    verification_code: str = Field(..., description="邮箱验证码", example="123456")
    new_password: str = Field(..., description="新密码", min_length=6, max_length=50, example="newpassword123")


class InviteInfo(BaseModel):
    id: int
    inviter_id: int
    invitee_id: int
    invitee_username: str
    invitee_email: str
    invite_code: str
    reward_credits: int
    is_rewarded: bool
    created_at: datetime


class UserInviteData(BaseModel):
    invite_code: str = Field(..., description="邀请码")
    invite_link: str = Field(..., description="邀请链接")
    total_invites: int = Field(..., description="总邀请数")
    total_rewards: int = Field(..., description="总奖励积分")
    invite_records: list[InviteInfo] = Field(..., description="邀请记录")
