from datetime import datetime
from typing import Optional
from tortoise.expressions import Q
from tortoise.queryset import QuerySet

from app.core.crud import CRUDBase
from app.models.admin import ProcessRecord
from app.schemas.process_records import ProcessRecordCreate, ProcessRecordUpdate, ProcessRecordQuery


class ProcessRecordController(CRUDBase[ProcessRecord, ProcessRecordCreate, ProcessRecordUpdate]):
    def __init__(self):
        super().__init__(model=ProcessRecord)

    async def get_by_user_doc_id(self, user_doc_id: str) -> Optional[ProcessRecord]:
        """根据user_doc_id获取记录"""
        return await self.model.filter(user_doc_id=user_doc_id).first()

    async def get_user_records(self, user_id: int, query: ProcessRecordQuery):
        """获取用户的处理记录列表"""
        q = Q(user_id=user_id)
        
        # 添加筛选条件
        if query.status:
            q &= Q(status=query.status)
        if query.process_mode:
            q &= Q(process_mode=query.process_mode)
        if query.process_type:
            q &= Q(process_type=query.process_type)

        # 计算总数
        total = await self.model.filter(q).count()
        
        # 分页查询
        offset = (query.page - 1) * query.page_size
        records = await self.model.filter(q).order_by("-created_at").offset(offset).limit(query.page_size)
        
        # 计算总页数
        total_pages = (total + query.page_size - 1) // query.page_size
        
        return {
            "items": records,
            "total": total,
            "page": query.page,
            "page_size": query.page_size,
            "total_pages": total_pages
        }

    async def create_record(self, obj_in: ProcessRecordCreate) -> ProcessRecord:
        """创建处理记录"""
        return await self.create(obj_in=obj_in)

    async def update_record_status(self, user_doc_id: str, status: str, error_message: Optional[str] = None) -> Optional[ProcessRecord]:
        """更新记录状态"""
        record = await self.get_by_user_doc_id(user_doc_id)
        if record:
            record.status = status
            if error_message:
                record.error_message = error_message
            await record.save()
        return record

    async def increment_download_count(self, user_doc_id: str) -> Optional[ProcessRecord]:
        """增加下载次数"""
        record = await self.get_by_user_doc_id(user_doc_id)
        if record:
            record.download_count += 1
            record.last_download_at = datetime.now()
            await record.save()
        return record

    async def get_user_stats(self, user_id: int) -> dict:
        """获取用户统计信息"""
        total_records = await self.model.filter(user_id=user_id).count()
        completed_records = await self.model.filter(user_id=user_id, status="completed").count()
        processing_records = await self.model.filter(user_id=user_id, status="processing").count()
        error_records = await self.model.filter(user_id=user_id, status="error").count()
        
        return {
            "total": total_records,
            "completed": completed_records,
            "processing": processing_records,
            "error": error_records
        }


process_record_controller = ProcessRecordController()
