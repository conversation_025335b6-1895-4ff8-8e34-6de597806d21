from typing import List, Optional
from tortoise.expressions import Q

from app.core.crud import CRUDBase
from app.models.admin import User, InviteRecord
from app.schemas.invites import InviteRecordCreate, InviteRecordUpdate, InviteRecordInfo
from app.schemas.auth import UserInviteData, InviteInfo
from app.settings.config import settings
from app.log import logger


class InviteController(CRUDBase[InviteRecord, InviteRecordCreate, InviteRecordUpdate]):
    def __init__(self):
        super().__init__(model=InviteRecord)

    async def get_user_invite_data(self, user_id: int) -> UserInviteData:
        """获取用户的推广数据"""
        try:
            # 获取用户信息
            user = await User.get(id=user_id)
            
            # 构建邀请链接
            invite_link = f"{settings.FRONTEND_URL}/register?aff={user.invite_code}"
            
            # 获取邀请记录
            invite_records = await InviteRecord.filter(inviter_id=user_id).prefetch_related()
            
            # 统计数据
            total_invites = len(invite_records)
            total_rewards = sum(record.reward_credits for record in invite_records if record.is_rewarded)
            
            # 构建邀请记录详情
            invite_infos = []
            for record in invite_records:
                # 获取被邀请人信息
                invitee = await User.get(id=record.invitee_id)
                invite_info = InviteInfo(
                    id=record.id,
                    inviter_id=record.inviter_id,
                    invitee_id=record.invitee_id,
                    invitee_username=invitee.username,
                    invitee_email=invitee.email,
                    invite_code=record.invite_code,
                    reward_credits=record.reward_credits,
                    is_rewarded=record.is_rewarded,
                    created_at=record.created_at
                )
                invite_infos.append(invite_info)
            
            # 按创建时间倒序排列
            invite_infos.sort(key=lambda x: x.created_at, reverse=True)
            
            return UserInviteData(
                invite_code=user.invite_code,
                invite_link=invite_link,
                total_invites=total_invites,
                total_rewards=total_rewards,
                invite_records=invite_infos
            )
            
        except Exception as e:
            logger.error(f"获取用户推广数据异常: user_id={user_id}, 错误: {str(e)}")
            raise

    async def get_invite_records_by_user(self, user_id: int, page: int = 1, page_size: int = 20) -> tuple[List[InviteRecordInfo], int]:
        """分页获取用户的邀请记录"""
        try:
            # 计算偏移量
            offset = (page - 1) * page_size
            
            # 查询邀请记录
            query = InviteRecord.filter(inviter_id=user_id)
            total = await query.count()
            
            records = await query.offset(offset).limit(page_size).order_by('-created_at')
            
            # 构建返回数据
            record_infos = []
            for record in records:
                # 获取被邀请人信息
                invitee = await User.get(id=record.invitee_id)
                record_info = InviteRecordInfo(
                    id=record.id,
                    inviter_id=record.inviter_id,
                    invitee_id=record.invitee_id,
                    invite_code=record.invite_code,
                    reward_credits=record.reward_credits,
                    is_rewarded=record.is_rewarded,
                    created_at=record.created_at,
                    updated_at=record.updated_at,
                    invitee_username=invitee.username,
                    invitee_email=invitee.email
                )
                record_infos.append(record_info)
            
            return record_infos, total
            
        except Exception as e:
            logger.error(f"获取邀请记录异常: user_id={user_id}, 错误: {str(e)}")
            raise

    async def get_invite_statistics(self, user_id: int) -> dict:
        """获取邀请统计数据"""
        try:
            # 总邀请数
            total_invites = await InviteRecord.filter(inviter_id=user_id).count()
            
            # 已奖励的邀请数
            rewarded_invites = await InviteRecord.filter(inviter_id=user_id, is_rewarded=True).count()
            
            # 总奖励积分
            records = await InviteRecord.filter(inviter_id=user_id, is_rewarded=True)
            total_rewards = sum(record.reward_credits for record in records)
            
            # 本月邀请数（简单实现，可以根据需要优化）
            from datetime import datetime, timedelta
            month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            month_invites = await InviteRecord.filter(
                inviter_id=user_id,
                created_at__gte=month_start
            ).count()
            
            return {
                "total_invites": total_invites,
                "rewarded_invites": rewarded_invites,
                "total_rewards": total_rewards,
                "month_invites": month_invites
            }
            
        except Exception as e:
            logger.error(f"获取邀请统计异常: user_id={user_id}, 错误: {str(e)}")
            raise

    async def validate_invite_code(self, invite_code: str) -> Optional[User]:
        """验证邀请码是否有效"""
        try:
            user = await User.filter(invite_code=invite_code).first()
            return user
        except Exception as e:
            logger.error(f"验证邀请码异常: invite_code={invite_code}, 错误: {str(e)}")
            return None


# 创建全局邀请控制器实例
invite_controller = InviteController()
